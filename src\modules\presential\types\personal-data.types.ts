/**
 * Types for Personal Data Step Components and Hooks
 * Following pattern: I<Action><Name><Scope>
 */

import { RefObject } from "react";
import Webcam from "react-webcam";

export interface IPersonalDataForm {
	name: string;
	cpf: string;
}

export interface IPersonalDataErrors {
	name?: string;
	cpf?: string;
	photo?: string;
}

export interface IValidateCpfHook {
	cpf: string;
	formattedCpf: string;
	isValid: boolean;
	error: string | null;
	handleCpfChange: (value: string) => void;
	validateCpf: (cpf: string) => boolean;
	formatCpf: (value: string) => string;
	clearError: () => void;
	reset: () => void;
}

export interface ICapturePhotoHook {
	photo: File | null;
	photoPreview: string | null;
	isWebcamOpen: boolean;
	webcamRef: RefObject<Webcam | null>;
	error: string | null;
	isCapturing: boolean;
	openWebcam: () => void;
	closeWebcam: () => void;
	capturePhoto: () => void;
	removePhoto: () => void;
	retakePhoto: () => void;
	reset: () => void;
}

export interface IHandlePersonalDataHook {
	formData: IPersonalDataForm;
	errors: IPersonalDataErrors;
	isFormValid: boolean;
	handleNameChange: (name: string) => void;
	cpfHook: IValidateCpfHook;
	photoHook: ICapturePhotoHook;
	validateForm: () => boolean;
	clearErrors: () => void;
	reset: () => void;
	syncWithGlobalState: () => void;
}

export interface IPersonalDataFormComponentProps {
	name: string;
	nameError?: string;
	cpfHook: IValidateCpfHook;
	onNameChange: (name: string) => void;
	className?: string;
}

export interface IPhotoCaptureComponentProps {
	photoHook: ICapturePhotoHook;
	error?: string;
	className?: string;
}

export interface IValidationStatusComponentProps {
	isValid: boolean;
	className?: string;
}

export interface IPersonalDataStepComponentProps {
	className?: string;
}
