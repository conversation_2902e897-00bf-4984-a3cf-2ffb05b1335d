import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/shared/components/ui/card";
import { User } from "lucide-react";
import { usePersonalData } from "@/modules/presential/hooks/epi/use-personal-data.hook";
import { PersonalDataFormComponent } from "../components/personal-data-form.component";
import { PhotoCaptureComponent } from "../components/photo-capture.component";
import { ValidationStatusComponent } from "../components/validation-status.component";

export const PersonalDataStep = () => {
	const personalDataHook = usePersonalData();

	const { formData, errors, isFormValid, handleNameChange, cpfHook, photoHook } = personalDataHook;
	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<User className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900"><PERSON><PERSON></h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Preencha os dados pessoais e adicione uma foto para identificação.</p>
			</div>

			{/* Form Card */}
			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-pormade">
						<User className="w-5 h-5" />
						Informações Pessoais
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						{/* Personal Data Form */}
						<PersonalDataFormComponent name={formData.name} nameError={errors.name} cpfHook={cpfHook} onNameChange={handleNameChange} />

						{/* Photo Capture */}
						<PhotoCaptureComponent photoHook={photoHook} error={errors.photo} />
					</div>
				</CardContent>
			</Card>
			{/* Validation Status */}
			<ValidationStatusComponent isValid={isFormValid} />
			<div className="text-center">
				<p className="text-sm text-gray-500 max-w-sm mx-auto">Ao clicar em próximo, você prosseguirá para a assinatura do documento.</p>
			</div>
		</div>
	);
};
