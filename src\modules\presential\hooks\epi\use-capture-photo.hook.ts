import { useCallback, useRef, useState } from "react";
import Webcam from "react-webcam";
import { ICapturePhotoHook } from "../../types/personal-data.types";

export const useCapturePhoto = (): ICapturePhotoHook => {
	const [photo, setPhoto] = useState<File | null>(null);
	const [photoPreview, setPhotoPreview] = useState<string | null>(null);
	const [isWebcamOpen, setIsWebcamOpen] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);
	const [isCapturing, setIsCapturing] = useState<boolean>(false);

	const webcamRef = useRef<Webcam>(null);

	const openWebcam = useCallback(() => {
		setError(null);
		setIsWebcamOpen(true);
	}, []);

	const closeWebcam = useCallback(() => {
		setIsWebcamOpen(false);
		setError(null);
	}, []);



	const dataURLtoFile = useCallback((dataurl: string, filename: string): File => {
		const arr = dataurl.split(",");
		const mime = arr[0].match(/:(.*?);/)?.[1] || "image/jpeg";
		const bstr = atob(arr[1]);
		let n = bstr.length;
		const u8arr = new Uint8Array(n);

		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}

		return new File([u8arr], filename, { type: mime });
	}, []);

	const capturePhoto = useCallback(() => {
		if (!webcamRef.current) {
			setError("Webcam não está disponível");
			return;
		}

		setIsCapturing(true);
		setError(null);

		try {
			const imageSrc = webcamRef.current.getScreenshot();

			if (!imageSrc) {
				setError("Não foi possível capturar a foto");
				setIsCapturing(false);
				return;
			}

			const timestamp = new Date().getTime();
			const filename = `photo_${timestamp}.jpg`;
			const photoFile = dataURLtoFile(imageSrc, filename);
			if (photoFile.size > 5 * 1024 * 1024) {
				setError("A foto capturada é muito grande (máximo 5MB)");
				setIsCapturing(false);
				return;
			}

			setPhoto(photoFile);
			setPhotoPreview(imageSrc);
			setIsWebcamOpen(false);
		} catch (err) {
			console.error("Erro ao capturar foto:", err);
			setError("Erro ao capturar a foto. Tente novamente.");
		} finally {
			setIsCapturing(false);
		}
	}, [dataURLtoFile]);

	const removePhoto = useCallback(() => {
		setPhoto(null);
		setPhotoPreview(null);
		setError(null);
	}, []);

	const retakePhoto = useCallback(() => {
		removePhoto();
		openWebcam();
	}, [removePhoto, openWebcam]);

	const reset = useCallback(() => {
		setPhoto(null);
		setPhotoPreview(null);
		setIsWebcamOpen(false);
		setError(null);
		setIsCapturing(false);
	}, []);

	return {
		photo,
		photoPreview,
		isWebcamOpen,
		webcamRef,
		error,
		isCapturing,
		openWebcam,
		closeWebcam,
		capturePhoto,
		removePhoto,
		retakePhoto,
		reset,
	};
};
