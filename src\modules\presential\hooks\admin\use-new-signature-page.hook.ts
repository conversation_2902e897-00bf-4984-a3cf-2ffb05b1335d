"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";

export interface IHandleNewSignaturePageActions {
	handleEpiSignatureClick(): void;
	handleOtherTypeClick(): void;
}

export interface INewSignaturePageState {
	isOtherTypeDisabled: boolean;
}

export interface IUseNewSignaturePageHook extends IHandleNewSignaturePageActions, INewSignaturePageState {}

export const useNewSignaturePage = (): IUseNewSignaturePageHook => {
	const router = useRouter();

	const handleEpiSignatureClick = useCallback(() => {
		router.push("/admin/epi/assinatura");
	}, [router]);

	const handleOtherTypeClick = useCallback(() => {
		console.log("Outros tipos de assinatura estarão disponíveis em breve");
	}, []);

	const isOtherTypeDisabled = true;

	return {
		handleEpiSignatureClick,
		handleOtherTypeClick,
		isOtherTypeDisabled,
	};
};
