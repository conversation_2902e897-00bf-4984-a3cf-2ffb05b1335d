import { cn } from "@/shared/lib/utils";
import { User } from "lucide-react";
import { IValidationStatusComponentProps } from "../../../../types/personal-data.types";

export const ValidationStatusComponent = ({ isValid, className }: IValidationStatusComponentProps) => {
	if (isValid) {
		return (
			<div className={cn("bg-green-50 p-4 rounded-lg border border-green-200", className)}>
				<div className="flex items-center gap-3">
					<div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
						<User className="w-4 h-4 text-white" />
					</div>
					<div className="flex-1">
						<p className="font-medium text-green-900">Dados Validados</p>
						<p className="text-sm text-green-700">Todos os campos foram preenchidos corretamente</p>
					</div>
					<div className="w-3 h-3 bg-green-500 rounded-full" />
				</div>
			</div>
		);
	}

	return (
		<div className={cn("bg-yellow-50 p-4 rounded-lg border border-yellow-200", className)}>
			<div className="flex items-center gap-3">
				<div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
					<User className="w-4 h-4 text-white" />
				</div>
				<div className="flex-1">
					<p className="font-medium text-yellow-900">Campos Pendentes</p>
					<p className="text-sm text-yellow-700">Preencha todos os campos obrigatórios para continuar</p>
				</div>
				<div className="w-3 h-3 bg-yellow-500 rounded-full" />
			</div>
		</div>
	);
};
