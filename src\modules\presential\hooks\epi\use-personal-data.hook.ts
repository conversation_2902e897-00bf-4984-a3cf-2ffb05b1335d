import { useCallback, useEffect, useState } from "react";
import { useEpiSignaturePage } from "./use-epi-signature-page.hook";
import { useCapturePhoto } from "./use-capture-photo.hook";
import { useValidateCpf } from "./use-validate-cpf.hook";
import { IPersonalDataForm, IPersonalDataErrors, IHandlePersonalDataHook } from "../../types/personal-data.types";

export const usePersonalData = (): IHandlePersonalDataHook => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();

	// Initialize form data from global state
	const [formData, setFormData] = useState<IPersonalDataForm>({
		name: signatureData.personName || "",
		cpf: signatureData.personCpf || "",
	});

	const [errors, setErrors] = useState<IPersonalDataErrors>({});

	// Initialize hooks
	const cpfHook = useValidateCpf(signatureData.personCpf || "");
	const photoHook = useCapturePhoto();

	// Sync photo from global state on mount
	useEffect(() => {
		if (signatureData.photo && !photoHook.photo) {
			// If there's a photo in global state but not in hook, create preview
			const reader = new FileReader();
			reader.onload = () => {
				// Note: This is a simplified approach. In a real scenario,
				// you might want to handle this differently
			};
			reader.readAsDataURL(signatureData.photo);
		}
	}, [signatureData.photo, photoHook.photo]);

	const handleNameChange = useCallback(
		(name: string) => {
			setFormData(prev => ({ ...prev, name }));

			// Clear name error if exists
			if (errors.name) {
				setErrors(prev => ({ ...prev, name: undefined }));
			}

			// Update global state
			updateSignatureData({ personName: name });
		},
		[errors.name, updateSignatureData]
	);

	const validateForm = useCallback((): boolean => {
		const newErrors: IPersonalDataErrors = {};
		let isValid = true;

		// Validate name
		if (!formData.name.trim() || formData.name.trim().length < 2) {
			newErrors.name = "Nome deve ter pelo menos 2 caracteres";
			isValid = false;
		}

		// Validate CPF using CPF hook
		if (!cpfHook.isValid) {
			newErrors.cpf = cpfHook.error || "CPF é obrigatório";
			isValid = false;
		}

		// Validate photo
		if (!photoHook.photo) {
			newErrors.photo = "Foto é obrigatória";
			isValid = false;
		}

		setErrors(newErrors);
		return isValid;
	}, [formData.name, cpfHook.isValid, cpfHook.error, photoHook.photo]);

	const clearErrors = useCallback(() => {
		setErrors({});
		cpfHook.clearError();
	}, [cpfHook]);

	const reset = useCallback(() => {
		setFormData({ name: "", cpf: "" });
		setErrors({});
		cpfHook.reset();
		photoHook.reset();
	}, [cpfHook, photoHook]);

	const syncWithGlobalState = useCallback(() => {
		// Sync all data with global state
		updateSignatureData({
			personName: formData.name,
			personCpf: cpfHook.cpf,
			photo: photoHook.photo || undefined,
		});
	}, [formData.name, cpfHook.cpf, photoHook.photo, updateSignatureData]);

	// Auto-sync CPF changes with global state
	useEffect(() => {
		if (cpfHook.cpf !== formData.cpf) {
			setFormData(prev => ({ ...prev, cpf: cpfHook.cpf }));
			updateSignatureData({ personCpf: cpfHook.cpf });
		}
	}, [cpfHook.cpf, formData.cpf, updateSignatureData]);

	// Auto-sync photo changes with global state
	useEffect(() => {
		updateSignatureData({ photo: photoHook.photo || undefined });
	}, [photoHook.photo, updateSignatureData]);

	// Calculate form validity
	const isFormValid = formData.name.trim().length >= 2 && cpfHook.isValid && photoHook.photo !== null && Object.keys(errors).length === 0;

	return {
		formData,
		errors,
		isFormValid,
		handleNameChange,
		cpfHook,
		photoHook,
		validateForm,
		clearErrors,
		reset,
		syncWithGlobalState,
	};
};
