import { useCallback, useState } from "react";
import { IValidateCpfHook } from "../../types/personal-data.types";

export const useValidateCpf = (initialValue: string = ""): IValidateCpfHook => {
	const [cpf, setCpf] = useState<string>(initialValue);
	const [error, setError] = useState<string | null>(null);

	const validateCpf = useCallback((cpf: string): boolean => {
		const cleanCPF = cpf.replace(/\D/g, "");

		// Verificar se tem 11 dígitos
		if (cleanCPF.length !== 11) return false;

		// Verificar se todos os dígitos são iguais
		if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

		// Validar primeiro dígito verificador
		let sum = 0;
		for (let i = 0; i < 9; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
		}
		let remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

		// Validar segundo dígito verificador
		sum = 0;
		for (let i = 0; i < 10; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
		}
		remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

		return true;
	}, []);

	const formatCpf = useCallback((value: string): string => {
		const cleanValue = value.replace(/\D/g, "");
		return cleanValue
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d{1,2})/, "$1-$2")
			.replace(/(-\d{2})\d+?$/, "$1");
	}, []);

	const handleCpfChange = useCallback(
		(value: string) => {
			const formattedValue = formatCpf(value);
			setCpf(formattedValue);

			// Limpar erro se existir
			if (error) {
				setError(null);
			}

			// Validar apenas se o CPF estiver completo
			const cleanValue = value.replace(/\D/g, "");
			if (cleanValue.length === 11) {
				const isValid = validateCpf(value);
				if (!isValid) {
					setError("CPF inválido");
				}
			}
		},
		[error, formatCpf, validateCpf]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const reset = useCallback(() => {
		setCpf("");
		setError(null);
	}, []);

	const isValid = cpf.replace(/\D/g, "").length === 11 && validateCpf(cpf) && !error;
	const formattedCpf = formatCpf(cpf);

	return {
		cpf,
		formattedCpf,
		isValid,
		error,
		handleCpfChange,
		validateCpf,
		formatCpf,
		clearError,
		reset,
	};
};
