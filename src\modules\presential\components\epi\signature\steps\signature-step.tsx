import { ViewTermModal } from "@/modules/presential/components/terms/modals/view-term";
import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { useFindTermByIdQuery } from "@/modules/presential/hooks/terms/find-term-by-id-query.hook";
import { SignatureCanvas } from "@/modules/signature/components/document-sign-page/pages/document-not-signed/signature-rubric-canvas";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Check, Edit, Eye, FileText, PenTool, RotateCcw } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

export const SignatureStep = () => {
	const [isViewTermModalOpen, setIsViewTermModalOpen] = useState(false);
	const [isRubricModalOpen, setIsRubricModalOpen] = useState(false);
	const [signatureSvg, setSignatureSvg] = useState<string | null>(null);

	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const { data: termData } = useFindTermByIdQuery(signatureData.selectedTermId || 0);
	const selectedTerm = termData?.success ? termData.data : null;

	const handleSaveSignature = useCallback((svg: string) => {
		setSignatureSvg(svg);
		setIsRubricModalOpen(false);
	}, []);

	const handleClearSignature = useCallback(() => {
		setSignatureSvg(null);
	}, []);

	// Salvar a assinatura automaticamente quando ela for criada
	useEffect(() => {
		if (signatureSvg) {
			updateSignatureData({
				...signatureData,
				signature: signatureSvg,
				termReadCompleted: true,
			});
		}
	}, [signatureSvg, signatureData, updateSignatureData]);

	return (
		<div className="space-y-6">
			<div className="text-center space-y-2">
				<div className="w-12 h-12 bg-pormade/10 rounded-full flex items-center justify-center mx-auto">
					<PenTool className="w-6 h-6 text-pormade" />
				</div>
				<h1 className="text-xl md:text-2xl font-bold text-gray-900">Assinatura do Termo</h1>
				<p className="text-sm text-gray-600 max-w-md mx-auto">Leia o termo e assine para confirmar o recebimento dos EPIs</p>
			</div>
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex  items-center gap-2 text-pormade">
							<FileText className="w-5 h-5" />
							Termo de Responsabilidade
						</CardTitle>
						<CardDescription>{selectedTerm ? "Leia atentamente antes de assinar" : "Carregando termo selecionado..."}</CardDescription>
					</CardHeader>
					<CardContent>
						{selectedTerm ? (
							<div className="bg-gray-50 p-3 md:p-4 rounded-lg">
								<div className="text-center text-gray-600 py-4 md:py-6">
									<FileText className="w-10 h-10 md:w-12 md:h-12 text-gray-400 mx-auto mb-3" />
									<p className="font-medium text-gray-700 mb-2">Termo Selecionado</p>
									<p className="text-sm text-gray-600 mb-3">{selectedTerm.title || selectedTerm.fileName}</p>
									<p className="text-xs text-gray-500 mb-4">
										Criado em: {new Date(selectedTerm.createDate).toLocaleDateString("pt-BR")}
									</p>
									<Button
										variant="outline"
										size="sm"
										className="border-pormade/30 text-pormade hover:bg-pormade/10 w-full sm:w-auto"
										onClick={() => setIsViewTermModalOpen(true)}
									>
										<Eye className="w-4 h-4 mr-2" />
										<span className="hidden sm:inline">Clique aqui para </span>Visualizar termo
									</Button>
								</div>
							</div>
						) : (
							<div className="bg-gray-50 p-3 md:p-4 rounded-lg">
								<div className="text-center text-gray-600 py-4 md:py-6">
									<div className="w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-lg mx-auto mb-3 animate-pulse" />
									<p className="text-sm text-gray-500">Carregando termo selecionado...</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-pormade">
							<PenTool className="w-5 h-5" />
							Assinatura Digital
						</CardTitle>
						<CardDescription>Adicione sua assinatura para confirmar o recebimento</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{signatureSvg ? (
							<div className="space-y-4">
								<div className="relative border-2 border-green-300 bg-green-50 rounded-lg p-4 flex items-center justify-center">
									<div className="w-full max-w-[300px] h-[150px]" dangerouslySetInnerHTML={{ __html: signatureSvg }} />
								</div>
								<div className="flex gap-2">
									<Button variant="outline" onClick={handleClearSignature} className="flex-1">
										<RotateCcw className="w-4 h-4 mr-2" />
										Limpar
									</Button>
									<Button onClick={() => setIsRubricModalOpen(true)} className="flex-1 bg-gray-800 hover:bg-gray-900 text-white">
										<Edit className="w-4 h-4 mr-2" />
										Refazer
									</Button>
								</div>
								<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
									<div className="flex items-center gap-2">
										<Check className="w-4 h-4 text-green-600" />
										<span className="text-sm font-medium text-green-800">Assinatura capturada com sucesso</span>
									</div>
								</div>
							</div>
						) : (
							<div className="space-y-4">
								<div className="bg-gray-50 p-6 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
									<PenTool className="w-10 h-10 text-gray-400 mb-3" />
									<p className="text-sm text-gray-600 text-center mb-1">Clique no botão abaixo para adicionar sua assinatura</p>
									<p className="text-xs text-gray-500 text-center">Use o mouse ou toque na tela para desenhar</p>
								</div>
								<Button
									onClick={() => setIsRubricModalOpen(true)}
									className="w-full h-12 bg-gray-800 hover:bg-gray-900 text-white font-medium"
								>
									<PenTool className="w-4 h-4 mr-2" />
									Adicionar Assinatura
								</Button>
							</div>
						)}
					</CardContent>{" "}
				</Card>
			</div>
			{isViewTermModalOpen && selectedTerm && (
				<ViewTermModal isOpen={isViewTermModalOpen} onClose={() => setIsViewTermModalOpen(false)} term={selectedTerm} />
			)}
			<Modal
				className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full"
				isOpen={isRubricModalOpen}
				onRequestClose={() => setIsRubricModalOpen(false)}
			>
				<div>
					<header className="text-center mb-4">
						<h2 className="text-xl font-semibold">Adicionar Assinatura</h2>
						<p className="text-sm text-gray-600">Desenhe sua assinatura abaixo e clique em Salvar.</p>
					</header>
					<SignatureCanvas onCancel={() => setIsRubricModalOpen(false)} onSave={handleSaveSignature} />
				</div>
				<p className="text-xs text-gray-500 mt-4 text-center">* Certifique-se de que sua assinatura esteja clara antes de salvar.</p>
			</Modal>
		</div>
	);
};
