"use client";

import { useNewSignaturePage } from "@/modules/presential/hooks/admin/use-new-signature-page.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { HardHat, Plus } from "lucide-react";

const NewSignaturePage = () => {
	const { handleEpiSignatureClick, handleOtherTypeClick, isOtherTypeDisabled } = useNewSignaturePage();

	return (
		<div className="space-y-6">
			<div className="flex flex-col p-4 md:p-6 bg-white shadow-sm rounded-lg border border-gray-200">
				<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-4">
					<div className="flex items-center gap-3 md:gap-4">
						<div className="bg-gray-50 p-2 md:p-3 rounded-lg flex items-center justify-center border border-gray-200">
							<Plus className="w-6 h-6 md:w-8 md:h-8 text-gray-600" />
						</div>
						<div>
							<h1 className="text-xl md:text-2xl font-medium text-gray-800">Nova Assinatura</h1>
							<p className="text-sm md:text-base text-gray-600">Selecione o tipo de assinatura que deseja criar</p>
						</div>
					</div>
				</div>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<Card className="cursor-pointer transition-all duration-200 hover:shadow-md hover:border-gray-300 group border-gray-200 flex flex-col h-full justify-between">
					<div className="flex-1 flex flex-col">
						<CardHeader className="text-center pb-4">
							<div className="mx-auto mb-4 p-4 bg-gray-50 rounded-lg w-fit group-hover:bg-gray-100 transition-colors border border-gray-200">
								<HardHat className="w-8 h-8 text-gray-700 group-hover:text-gray-800" />
							</div>
							<CardTitle className="text-lg font-medium text-gray-800">Assinatura de EPI</CardTitle>
							<CardDescription className="text-gray-600 text-sm">
								Criar assinatura para retirada ou devolução de Equipamentos de Proteção Individual
							</CardDescription>
						</CardHeader>
					</div>
					<CardContent className="pt-0 mt-auto">
						<Button
							onClick={handleEpiSignatureClick}
							className="w-full bg-gray-800 hover:bg-gray-700 text-white font-medium py-2.5 rounded-md transition-colors"
							aria-label="Criar assinatura de EPI"
						>
							Selecionar
						</Button>
					</CardContent>
				</Card>
				<Card className="transition-all duration-200 cursor-not-allowed border-gray-200 bg-gray-50/50 flex flex-col h-full justify-between">
					<div className="flex-1 flex flex-col">
						<CardHeader className="text-center pb-4">
							<div className="mx-auto mb-4 p-4 bg-gray-100 rounded-lg w-fit border border-gray-200">
								<Plus className="w-8 h-8 text-gray-400" />
							</div>
							<CardTitle className="text-lg font-medium text-gray-500">Outro Tipo</CardTitle>
							<CardDescription className="text-gray-400 text-sm">
								Outros tipos de assinatura estarão disponíveis em breve
							</CardDescription>
						</CardHeader>
					</div>
					<CardContent className="pt-0 mt-auto">
						<Button
							onClick={handleOtherTypeClick}
							disabled={isOtherTypeDisabled}
							className="w-full font-medium py-2.5 rounded-md transition-colors bg-gray-300 text-gray-500 cursor-not-allowed"
							variant="secondary"
							aria-label="Outro tipo de assinatura - Em breve"
							aria-disabled="true"
						>
							Em Breve
						</Button>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default NewSignaturePage;
