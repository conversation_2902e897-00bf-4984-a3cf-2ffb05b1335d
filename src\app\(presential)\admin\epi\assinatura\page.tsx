"use client";

import { EpiSignatureStepper } from "@/modules/presential/components/epi/signature/epi-signature-stepper";
import { useEpiSignatureStepper } from "@/modules/presential/hooks/epi/use-epi-signature-stepper.hook";
import { Button } from "@/shared/components/ui/button";
import * as Dialog from "@radix-ui/react-dialog";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

const EPISignaturePage = () => {
	const { isFirstStep } = useEpiSignatureStepper();
	const [open, setOpen] = useState(false);
	const router = useRouter();

	const handleBackClick = (e: React.MouseEvent) => {
		e.preventDefault();
		if (isFirstStep) {
			router.push("/admin/nova-assinatura");
		} else {
			setOpen(true);
		}
	};

	const handleConfirmExit = () => {
		setOpen(false);
		router.push("/admin/nova-assinatura");
	};

	return (
		<div className="md:p-6">
			<div className="mb-6">
				<Dialog.Root open={open} onOpenChange={setOpen}>
					<Button variant="outline" className="gap-2" onClick={handleBackClick}>
						<ArrowLeft className="w-4 h-4" />
						Voltar
					</Button>
					<Dialog.Portal>
						<Dialog.Overlay className="fixed inset-0 bg-black/40 z-50" />
						<Dialog.Content className="fixed left-1/2 top-1/2 z-50 w-full max-w-md -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white p-6 shadow-lg flex flex-col gap-4">
							<Dialog.Title className="text-lg font-semibold">Sair do processo?</Dialog.Title>
							<Dialog.Description className="text-gray-600">
								Se você sair agora, as informações preenchidas serão perdidas. Tem certeza que deseja sair?
							</Dialog.Description>
							<div className="flex justify-end gap-2 mt-4">
								<Dialog.Close asChild>
									<Button variant="secondary">Cancelar</Button>
								</Dialog.Close>
								<Button variant="destructive" onClick={handleConfirmExit}>
									Sair
								</Button>
							</div>
						</Dialog.Content>
					</Dialog.Portal>
				</Dialog.Root>
			</div>
			<EpiSignatureStepper />
		</div>
	);
};

export default EPISignaturePage;
