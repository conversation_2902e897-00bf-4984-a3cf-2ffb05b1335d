import { useAtomValue, useSetAtom } from "jotai";
import {
	canProceedAtom,
	currentStepAtom,
	IEpiSignatureData,
	isLoadingAtom,
	nextStepAtom,
	previousStepAtom,
	resetProcessAtom,
	setStepAtom,
	signatureData<PERSON>tom,
	updateSignatureData<PERSON>tom,
} from "../../atoms/epi-signature.atoms";

interface ISelectedEpiItem {
	id: number;
	name: string;
	description?: string;
	quantity: number;
	needsReturn: boolean;
}

export interface IHandleEpiSignaturePageHook {
	currentStep: number;
	isLoading: boolean;
	canProceed: boolean;
	signatureData: IEpiSignatureData;
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	updateSignatureData: (data: Partial<IEpiSignatureData>) => void;
	resetProcess: () => void;
	validateCurrentStep: () => boolean;
	handleConfirmSignature: () => void;
}

export const useEpiSignaturePage = (): IHandleEpiSignaturePageHook => {
	// Atoms values
	const currentStep = useAtomValue(currentStepAtom);
	const isLoading = useAtomValue(isLoadingAtom);
	const canProceed = useAtomValue(canProceedAtom);
	const signatureData = useAtomValue(signatureDataAtom);

	// Atoms actions
	const handleNextStep = useSetAtom(nextStepAtom);
	const handlePreviousStep = useSetAtom(previousStepAtom);
	const handleStepChange = useSetAtom(setStepAtom);
	const updateSignatureData = useSetAtom(updateSignatureDataAtom);
	const resetProcess = useSetAtom(resetProcessAtom);

	const validateCurrentStep = (): boolean => {
		return canProceed;
	};

	const handleConfirmSignature = (): void => {
		const groupEpi = ((signatureData.selectedEpis as ISelectedEpiItem[]) || []).map((epi: ISelectedEpiItem) => ({
			idEpi: epi.id,
			quantity: epi.quantity || 1,
			requireGiveBack: epi.needsReturn || false,
			observation: epi.description || "",
		}));

		const apiData = {
			idTerm: signatureData.selectedTermId || 0,
			groupEpi,
			userCpf: signatureData.personCpf || "",
			userName: signatureData.personName || "",
		};

		console.log("=== DADOS COLETADOS NO STEPPER ===");
		console.log(JSON.stringify(apiData, null, 2));

		console.log("=== DADOS COMPLETOS PARA DEBUG ===");
		console.log("Dados completos da assinatura:", {
			personName: signatureData.personName,
			personCpf: signatureData.personCpf,
			selectedPersonId: signatureData.selectedPersonId,
			selectedTermId: signatureData.selectedTermId,
			selectedEpiGroupId: signatureData.selectedEpiGroupId,
			selectedEpis: signatureData.selectedEpis,
			epiItems: signatureData.epiItems,
			photo: signatureData.photo ? "Foto capturada" : "Sem foto",
			photoUrl: signatureData.photo ? URL.createObjectURL(signatureData.photo) : null,
			signature: signatureData.signature,
			termReadCompleted: true,
			timestamp: new Date().toISOString(),
		});

		// Avançar para o próximo passo após confirmação
		handleNextStep();
	};

	return {
		currentStep,
		isLoading,
		canProceed,
		signatureData,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		updateSignatureData,
		resetProcess,
		validateCurrentStep,
		handleConfirmSignature,
	};
};
