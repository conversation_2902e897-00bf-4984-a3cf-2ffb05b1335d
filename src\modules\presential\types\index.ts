//Dada uma string contendo dígitos de2-9 ≤ ...

// Um mapeamento de dígitos para letras (assim como nas teclas do telefone) é apresentado abaixo. Observe que 1 não é mapeado para nenhuma letra.

function letterCombinations(digits: string): string[] {
    const mapping: Record<string, string[]> = {
        2: ['a', 'b', 'c'],
        3: ['d', 'e', 'f'],
        4: ['g', 'h', 'i'],
        5: ['j', 'k', 'l'],
        6: ['m', 'n', 'o'],
        7: ['p', 'q', 'r', 's'],
        8: ['t', 'u', 'v'],
        9: ['w', 'x', 'y', 'z']
    };

    const result: string[] = [];

    const backtrack = (index: number, current: string) => {

    if (current.length === 0) {
        return []
    }

        if (current.length === digits.length) {
            result.push(current);
            return;
        }

        const digit = digits[index];
        const letters = mapping[digit];

        if (!letters) return;

        for (const letter of letters) {
            backtrack(index + 1, current + letter);
        }
    };

    backtrack(0, "");
    return result;
}

