import { Button } from "@/shared/components/ui/button";
import { Label } from "@/shared/components/ui/label";
import { Sheet, SheetContent } from "@/shared/components/ui/sheet";
import { cn } from "@/shared/lib/utils";
import { <PERSON>ertTriangle, Camera, RotateCcw, Settings, X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import Webcam from "react-webcam";
import { IPhotoCaptureComponentProps } from "../../../../types/personal-data.types";

const videoConstraints = {
	width: 1280,
	height: 720,
	facingMode: "user",
};

export const PhotoCaptureComponent = ({ photoHook, error, className }: IPhotoCaptureComponentProps) => {
	const {
		photoPreview,
		isWebcamOpen,
		webcamRef,
		error: photoError,
		isCapturing,
		openWebcam,
		closeWebcam,
		capturePhoto,
		removePhoto,
		retakePhoto,
	} = photoHook;

	const [cameraPermission, setCameraPermission] = useState<"granted" | "denied" | "prompt" | "checking">("checking");
	const [hasCameraDevice, setHasCameraDevice] = useState<boolean | null>(null);
	const [webcamError, setWebcamError] = useState<string | null>(null);

	const displayError = error || photoError || webcamError;

	const checkCameraDevices = async () => {
		try {
			const devices = await navigator.mediaDevices.enumerateDevices();
			const videoDevices = devices.filter(device => device.kind === "videoinput");
			setHasCameraDevice(videoDevices.length > 0);
		} catch {
			setHasCameraDevice(false);
			setWebcamError("Erro ao verificar dispositivos de câmera");
		}
	};

	const checkCameraPermission = async () => {
		try {
			if (!navigator.permissions) {
				setCameraPermission("prompt");
				return;
			}
			const permission = await navigator.permissions.query({ name: "camera" as PermissionName });
			setCameraPermission(permission.state as "granted" | "denied" | "prompt");
			permission.onchange = () => {
				setCameraPermission(permission.state as "granted" | "denied" | "prompt");
			};
		} catch {
			setCameraPermission("prompt");
		}
	};

	useEffect(() => {
		checkCameraDevices();
		checkCameraPermission();
	}, []);

	const requestCameraPermission = async () => {
		try {
			setWebcamError(null);
			await navigator.mediaDevices.getUserMedia({ video: true });
			setCameraPermission("granted");
			openWebcam();
		} catch (error: unknown) {
			if (error instanceof DOMException) {
				if (error.name === "NotAllowedError") {
					setCameraPermission("denied");
					setWebcamError("Permissão da câmera negada. Verifique as configurações do navegador.");
				} else if (error.name === "NotFoundError") {
					setHasCameraDevice(false);
					setWebcamError("Nenhuma câmera encontrada no dispositivo");
				} else if (error.name === "NotReadableError") {
					setWebcamError("Câmera está sendo usada por outro aplicativo");
				} else {
					setWebcamError("Erro ao acessar a câmera. Tente novamente.");
				}
			} else {
				setWebcamError("Erro ao acessar a câmera. Tente novamente.");
			}
		}
	};

	const openSystemSettings = () => {
		if (typeof window !== "undefined" && "electron" in window) {
			// Se estiver em Electron, pode tentar abrir configurações do sistema
		} else {
			alert(
				"Para conectar uma câmera:\n\n1. Conecte uma câmera USB ao computador\n2. Aguarde o Windows instalar os drivers\n3. Recarregue esta página\n\nOu acesse as Configurações do Windows > Privacidade > Câmera"
			);
		}
	};

	const handleOpenWebcam = () => {
		if (hasCameraDevice === false) return;
		if (cameraPermission === "denied") {
			setWebcamError("Permissão da câmera negada. Verifique as configurações do navegador e permita o acesso à câmera.");
			return;
		}
		if (cameraPermission === "granted") {
			openWebcam();
		} else {
			requestCameraPermission();
		}
	};

	const handleWebcamError = (error: string | DOMException) => {
		if (typeof error === "string") {
			setWebcamError(error);
		} else {
			switch (error.name) {
				case "NotAllowedError":
					setCameraPermission("denied");
					setWebcamError("Permissão da câmera negada. Verifique as configurações do navegador.");
					break;
				case "NotFoundError":
					setHasCameraDevice(false);
					setWebcamError("Nenhuma câmera encontrada no dispositivo");
					break;
				case "NotReadableError":
					setWebcamError("Câmera está sendo usada por outro aplicativo");
					break;
				default:
					setWebcamError("Erro ao acessar a câmera. Tente novamente.");
			}
		}
		closeWebcam();
	};

	const handleCloseWebcam = () => {
		setWebcamError(null);
		closeWebcam();
	};

	return (
		<div className={cn("space-y-2", className)}>
			<Label className="flex items-center gap-2">
				<Camera className="w-4 h-4" />
				Foto *
			</Label>

			{photoPreview ? (
				<div className="space-y-3">
					<div className="relative">
						<Image
							src={photoPreview}
							alt="Foto capturada"
							width={400}
							height={192}
							className="w-full h-48 object-cover rounded-lg border"
						/>
						<Button variant="destructive" size="icon" className="absolute top-2 right-2" onClick={removePhoto} type="button">
							<X className="w-4 h-4" />
						</Button>
					</div>
					<div className="flex gap-2">
						<Button variant="outline" onClick={retakePhoto} className="flex-1" size="sm" type="button">
							<RotateCcw className="w-4 h-4 mr-2" />
							Refazer
						</Button>
					</div>
				</div>
			) : (
				<div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
					{hasCameraDevice === false ? (
						<>
							<AlertTriangle className="w-8 h-8 text-amber-500 mx-auto mb-3" />
							<p className="text-sm text-gray-600 mb-3">Nenhuma câmera disponível</p>
							<p className="text-xs text-gray-500 mb-4">Conecte uma câmera USB ou verifique se a câmera integrada está funcionando</p>
							<div className="flex flex-col gap-2">
								<Button variant="outline" onClick={checkCameraDevices} className="w-full" size="sm" type="button">
									<RotateCcw className="w-4 h-4 mr-2" />
									Verificar Novamente
								</Button>
								<Button variant="outline" onClick={openSystemSettings} className="w-full" size="sm" type="button">
									<Settings className="w-4 h-4 mr-2" />
									Configurações do Sistema
								</Button>
							</div>
						</>
					) : cameraPermission === "denied" ? (
						<>
							<AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
							<p className="text-sm text-gray-600 mb-3">Acesso à câmera negado</p>
							<p className="text-xs text-gray-500 mb-4">
								Para capturar fotos, é necessário permitir o acesso à câmera nas configurações do navegador
							</p>
							<Button variant="outline" onClick={requestCameraPermission} className="w-full" size="sm" type="button">
								<Camera className="w-4 h-4 mr-2" />
								Solicitar Permissão
							</Button>
						</>
					) : cameraPermission === "checking" || hasCameraDevice === null ? (
						<>
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-3"></div>
							<p className="text-sm text-gray-600 mb-3">Verificando câmera...</p>
						</>
					) : (
						<>
							<Camera className="w-8 h-8 text-gray-400 mx-auto mb-3" />
							<p className="text-sm text-gray-600 mb-3">Nenhuma foto capturada</p>
							<Button
								onClick={handleOpenWebcam}
								className="w-full bg-gray-800 text-white font-semibold text-base py-3 rounded-xl shadow-lg flex items-center justify-center gap-2 active:scale-95 transition-all border-0"
								size="lg"
								type="button"
							>
								<Camera className="w-5 h-5" />
								Capturar Foto
							</Button>
						</>
					)}
				</div>
			)}

			{displayError && <p className="text-sm text-red-600">{displayError}</p>}

			<Sheet open={isWebcamOpen} onOpenChange={handleCloseWebcam}>
				<SheetContent side="bottom" className="h-[100dvh] max-h-none p-0 flex flex-col bg-black/95 sm:rounded-none sm:p-0">
					<div className="absolute top-0 left-0 w-full z-20 flex items-center justify-center px-4 pt-5 pointer-events-none">
						<span className="flex items-center gap-2 text-white text-lg font-semibold bg-black/60 rounded-full px-4 py-2 shadow-md pointer-events-auto select-none">
							<Camera className="w-5 h-5" /> Capturar Foto
						</span>
					</div>

					<div className="absolute bottom-0 left-0 w-full z-20 flex flex-col items-center justify-end px-4 pb-6 pointer-events-none">
						<div className="w-full max-w-md flex gap-4 pointer-events-auto">
							<Button
								variant="outline"
								onClick={handleCloseWebcam}
								className="flex-1 border-white text-white bg-black/40 hover:text-white hover:bg-white/10 font-semibold py-3 rounded-xl text-base shadow-md backdrop-blur-md"
								disabled={isCapturing}
								type="button"
							>
								Cancelar
							</Button>
							<Button
								onClick={capturePhoto}
								className="flex-1 bg-gradient-to-r from-gray-800 to-gray-600 text-white font-semibold text-base py-3 rounded-xl shadow-md flex items-center justify-center gap-2 active:scale-95 transition-all border-0"
								disabled={isCapturing}
								type="button"
							>
								<Camera className="w-5 h-5" />
								{isCapturing ? "Capturando..." : "Capturar"}
							</Button>
						</div>
					</div>
					<div className="relative flex-1 flex items-center justify-center w-full h-full">
						<Webcam
							ref={webcamRef}
							audio={false}
							screenshotFormat="image/jpeg"
							videoConstraints={videoConstraints}
							className="w-full h-full object-cover absolute inset-0"
							onUserMediaError={handleWebcamError}
						/>
						{isCapturing && (
							<div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center z-30">
								<div className="text-white text-center">
									<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
									<p>Capturando...</p>
								</div>
							</div>
						)}
					</div>
					{photoError && <p className="text-sm text-red-400 text-center mt-4 z-30 absolute bottom-4 left-0 w-full">{photoError}</p>}
				</SheetContent>
			</Sheet>
		</div>
	);
};
