import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cn } from "@/shared/lib/utils";
import { IPersonalDataFormComponentProps } from "../../../../types/personal-data.types";

export const PersonalDataFormComponent = ({ name, nameError, cpfHook, onNameChange, className }: IPersonalDataFormComponentProps) => {
	const { cpf, error: cpfError, handleCpfChange } = cpfHook;

	return (
		<div className={cn("space-y-4", className)}>
			<div className="space-y-2">
				<Label htmlFor="name">Nome Completo *</Label>
				<Input
					id="name"
					type="text"
					placeholder="Digite o nome completo"
					value={name}
					onChange={e => onNameChange(e.target.value)}
					className={cn("transition-colors", nameError ? "border-red-500 focus:border-red-500" : "")}
				/>
				{nameError && <p className="text-sm text-red-600">{nameError}</p>}
			</div>
			<div className="space-y-2">
				<Label htmlFor="cpf">CPF *</Label>
				<Input
					id="cpf"
					type="text"
					placeholder="000.000.000-00"
					value={cpf}
					onChange={e => handleCpfChange(e.target.value)}
					maxLength={14}
					className={cn("transition-colors", cpfError ? "border-red-500 focus:border-red-500" : "")}
				/>
				{cpfError && <p className="text-sm text-red-600">{cpfError}</p>}
			</div>
		</div>
	);
};
